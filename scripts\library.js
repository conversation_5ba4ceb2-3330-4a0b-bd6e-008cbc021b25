// Library Page JavaScript

document.addEventListener("DOMContentLoaded", () => {
  // Initialize library functionality
  initializeLibrary();

  // Initialize search functionality for library
  initializeLibrarySearch();
});

// Initialize library functionality
function initializeLibrary() {
  // Get library elements
  const emptyLibrary = document.getElementById("empty-library");
  const librarySongs = document.getElementById("library-songs");

  // If library elements don't exist, return
  if (!emptyLibrary || !librarySongs) return;

  // Get library songs from localStorage
  const librarySongsData = getLibrarySongs();

  // Check if library is empty
  if (librarySongsData.length === 0) {
    // Show empty library message
    emptyLibrary.style.display = "flex";
    librarySongs.style.display = "none";
  } else {
    // Hide empty library message and show songs
    emptyLibrary.style.display = "none";
    librarySongs.style.display = "grid";

    // Populate library songs
    populateLibrarySongs(librarySongsData);
  }
}

// Get library songs from localStorage
function getLibrarySongs() {
  // Get library songs from localStorage
  const librarySongs = localStorage.getItem("librarySongs");

  // If library songs exist, parse and return them
  if (librarySongs) {
    return JSON.parse(librarySongs);
  }

  // Otherwise, return empty array
  return [];
}

// Save library songs to localStorage
function saveLibrarySongs(songs) {
  // Save library songs to localStorage
  localStorage.setItem("librarySongs", JSON.stringify(songs));
}

// Add song to library
function addSongToLibrary(songId) {
  // Get library songs
  const librarySongs = getLibrarySongs();

  // Check if song already exists in library
  if (librarySongs.includes(songId)) {
    return false; // Song already in library
  }

  // Add song to library
  librarySongs.push(songId);

  // Save updated library
  saveLibrarySongs(librarySongs);

  // Show notification
  if (typeof showNotification === "function") {
    showNotification("Thêm vào danh sách thành công", 3000);
  }

  return true; // Song added successfully
}

// Remove song from library
function removeSongFromLibrary(songId) {
  // Get library songs
  let librarySongs = getLibrarySongs();

  // Remove song from library
  librarySongs = librarySongs.filter((id) => id !== songId);

  // Save updated library
  saveLibrarySongs(librarySongs);

  // If on library page, refresh the display
  if (window.location.pathname.includes("library.html")) {
    initializeLibrary();
  }

  return true; // Song removed successfully
}

// Check if song is in library
function isSongInLibrary(songId) {
  // Get library songs
  const librarySongs = getLibrarySongs();

  // Check if song is in library
  return librarySongs.includes(songId);
}

// Populate library songs
function populateLibrarySongs(librarySongIds) {
  // Get library songs container
  const librarySongsContainer = document.getElementById("library-songs");

  // Clear existing songs
  librarySongsContainer.innerHTML = "";

  // Filter songs array to get only library songs
  const libraryTracks = songs.filter((song) =>
    librarySongIds.includes(song.id)
  );

  // Add songs to container
  libraryTracks.forEach((song) => {
    const songElement = document.createElement("div");
    songElement.className = "library-song-item";
    songElement.innerHTML = `
      <div class="img_play">
        <img src="${song.poster}" alt="Song Cover">
        <i class="bi playListPlay bi-play-circle-fill" id="${song.id}"></i>
      </div>
      <h5>${extractSongName(song.songName)}</h5>
      <div class="subtitle">${extractArtistName(song.songName)}</div>
      <div class="song-actions">
        <i class="bi bi-heart-fill add-to-library in-library" data-id="${
          song.id
        }" title="Remove from Library"></i>
        <i class="bi bi-trash remove-from-library" data-id="${
          song.id
        }" title="Remove from Library"></i>
      </div>
    `;

    // Add click event to navigate to the song detail page
    songElement.addEventListener("click", (e) => {
      // Only navigate if the click is not on the play button, heart icon, or remove button
      if (
        !e.target.classList.contains("playListPlay") &&
        !e.target.classList.contains("remove-from-library") &&
        !e.target.classList.contains("add-to-library")
      ) {
        window.location.href = `music-detail.html?id=${song.id}`;
      }
    });

    librarySongsContainer.appendChild(songElement);
  });

  // Add event listeners to play buttons
  Array.from(
    document.querySelectorAll(".library-song-item .playListPlay")
  ).forEach((element) => {
    element.addEventListener("click", (e) => {
      const songId = e.target.id;
      playSong(songId);
    });
  });

  // Add event listeners to heart icons
  Array.from(
    document.querySelectorAll(".library-song-item .add-to-library")
  ).forEach((element) => {
    element.addEventListener("click", (e) => {
      e.stopPropagation(); // Prevent navigation to detail page
      const songId = e.target.getAttribute("data-id");

      if (isSongInLibrary(songId)) {
        if (removeSongFromLibrary(songId)) {
          // Sync all heart icons
          if (typeof syncAllHeartIcons === "function") {
            syncAllHeartIcons(songId);
          }
          // Refresh library display since song was removed
          initializeLibrary();
        }
      }
    });
  });

  // Add event listeners to remove buttons
  Array.from(document.querySelectorAll(".remove-from-library")).forEach(
    (element) => {
      element.addEventListener("click", (e) => {
        e.stopPropagation(); // Prevent navigation to detail page
        const songId = e.target.getAttribute("data-id");
        if (removeSongFromLibrary(songId)) {
          // Sync all heart icons
          if (typeof syncAllHeartIcons === "function") {
            syncAllHeartIcons(songId);
          }
          // Refresh library display since song was removed
          initializeLibrary();
        }
      });
    }
  );
}

// Play a song
function playSong(songId) {
  // Set the current song index
  index = parseInt(songId);

  // Update the music player
  music.src = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;

  // Find the song in the songs array
  const song = songs.find((song) => song.id === songId);

  // Update the title
  title.innerHTML = song.songName;

  // Update download link
  download_music.href = `./audio/${index}.mp3`;
  download_music.setAttribute("download", extractSongName(song.songName));

  // Play the song
  music.play();

  // Update UI
  masterPlay.classList.remove("bi-play-fill");
  masterPlay.classList.add("bi-pause-fill");

  // Add wave animation
  wave.classList.add("active2");

  // Make all play buttons to play icon
  makeAllPlays();

  // Update the play button for current song
  const playButton = document.getElementById(`${index}`);
  if (playButton) {
    playButton.classList.remove("bi-play-circle-fill");
    playButton.classList.add("bi-pause-circle-fill");
  }

  // Add to recently played history
  if (typeof addSongToRecentlyPlayed === "function") {
    addSongToRecentlyPlayed(songId);
  }

  // Update master heart icon
  if (typeof updateMasterHeartIcon === "function") {
    updateMasterHeartIcon();
  }
}

// Initialize search functionality for library
function initializeLibrarySearch() {
  // Get search elements
  const searchInput = document.querySelector(".search input");

  // If search input doesn't exist, return
  if (!searchInput) return;

  // Add search input event listener
  searchInput.addEventListener("input", () => {
    const searchValue = searchInput.value.toLowerCase();

    // Get all library song items
    const songItems = document.querySelectorAll(".library-song-item");

    // Filter songs based on search value
    songItems.forEach((item) => {
      const songTitle = item.querySelector("h5").textContent.toLowerCase();
      const artistName = item
        .querySelector(".subtitle")
        .textContent.toLowerCase();

      if (songTitle.includes(searchValue) || artistName.includes(searchValue)) {
        item.style.display = "block";
      } else {
        item.style.display = "none";
      }
    });
  });
}

// Extract song name from HTML string
function extractSongName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the text content (song name) without the subtitle
  const songName = tempDiv.childNodes[0].textContent.trim();
  return songName;
}

// Extract artist name from HTML string
function extractArtistName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the subtitle div content (artist name)
  const artistElement = tempDiv.querySelector(".subtitle");
  return artistElement ? artistElement.textContent.trim() : "Unknown Artist";
}
